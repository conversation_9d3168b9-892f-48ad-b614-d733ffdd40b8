import { useMutation, useQueryClient, type UseMutationOptions } from "@tanstack/react-query";
import { APIROUTES } from "@/helpers/apiRoutes";
import { orpc } from "@/lib/orpc";

interface CancelMiningResponse {
    success: boolean;
    message: string;
}

const useCancelMining = (options: Partial<UseMutationOptions<CancelMiningResponse, Error, void>> = {}) => {
    const queryClient = useQueryClient();

    return useMutation(
        orpc.skills.cancelMining.mutationOptions({
            onSuccess: () => {
                queryClient.invalidateQueries({ queryKey: APIROUTES.MINING.SESSION });
            },
            ...options,
        })
    );
};

export default useCancelMining;
