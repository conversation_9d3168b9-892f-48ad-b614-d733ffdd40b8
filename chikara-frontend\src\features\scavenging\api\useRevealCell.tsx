import type { Reveal<PERSON>ellParams, RevealCellResponse } from "@/features/scavenging/types/index";
import { APIROUTES } from "@/helpers/apiRoutes";
import { orpc } from "@/lib/orpc";
import { type UseMutationOptions, useMutation, useQueryClient } from "@tanstack/react-query";

const useRevealCell = (options: Partial<UseMutationOptions<RevealCellResponse, Error, RevealCellParams>> = {}) => {
    const queryClient = useQueryClient();

    return useMutation(
        orpc.skills.scavenging.revealCell.mutationOptions({
            onSuccess: (data) => {
                queryClient.invalidateQueries({ queryKey: APIROUTES.SCAVENGING.ACTIVE_SESSION });
                return data;
            },
            ...options,
        })
    );
};

export default useRevealCell;
