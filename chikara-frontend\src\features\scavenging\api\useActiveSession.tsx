import type { ActiveSessionResponse } from "@/features/scavenging/types/index";
import { orpc, QueryOptions } from "@/lib/orpc";
import { useQuery } from "@tanstack/react-query";

const useActiveSession = (options: QueryOptions = {}) => {
    return useQuery(
        orpc.skills.scavenging.getActiveSession.queryOptions({
            staleTime: 30000, // 30 seconds
            ...options,
        })
    );
};

export default useActiveSession;
