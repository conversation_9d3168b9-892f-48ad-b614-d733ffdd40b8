import { useMutation, useQueryClient, type UseMutationOptions } from "@tanstack/react-query";
import { APIROUTES } from "@/helpers/apiRoutes";
import { orpc } from "@/lib/orpc";
import type { MiningSession, StartMiningParams } from "@/features/mining/types/mining";

const useStartMining = (options: Partial<UseMutationOptions<MiningSession, Error, StartMiningParams>> = {}) => {
    const queryClient = useQueryClient();

    return useMutation(
        orpc.skills.startMining.mutationOptions({
            onSuccess: () => {
                queryClient.invalidateQueries({ queryKey: APIROUTES.MINING.SESSION });
            },
            ...options,
        })
    );
};

export default useStartMining;
