import { orpc } from "@/lib/orpc";
import { type UseMutationOptions, useMutation } from "@tanstack/react-query";
import type { DevGridResponse } from "../types";

interface DevGridParams {
    sessionId: string;
}

const useDevGrid = (options: Partial<UseMutationOptions<DevGridResponse, Error, DevGridParams>> = {}) => {
    return useMutation(
        orpc.skills.scavenging.devGrid.mutationOptions({
            ...options,
        })
    );
};

export default useDevGrid;
