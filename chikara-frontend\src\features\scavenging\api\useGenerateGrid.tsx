import type { GenerateGridParams, GenerateGridResponse } from "@/features/scavenging/types/index";
import { APIROUTES } from "@/helpers/apiRoutes";
import { orpc } from "@/lib/orpc";
import { type UseMutationOptions, useMutation, useQueryClient } from "@tanstack/react-query";

const useGenerateGrid = (
    options: Partial<UseMutationOptions<GenerateGridResponse, Error, GenerateGridParams>> = {}
) => {
    const queryClient = useQueryClient();

    return useMutation(
        orpc.skills.scavenging.generateGrid.mutationOptions({
            onSuccess: () => {
                queryClient.invalidateQueries({ queryKey: APIROUTES.SCAVENGING.ACTIVE_SESSION });
            },
            ...options,
        })
    );
};

export default useGenerateGrid;
