import { useMutation, useQueryClient, type UseMutationOptions } from "@tanstack/react-query";
import { APIROUTES } from "@/helpers/apiRoutes";
import { orpc } from "@/lib/orpc";
import type { SwingResult, ProcessSwingParams } from "@/features/mining/types/mining";

const useProcessSwing = (options: Partial<UseMutationOptions<SwingResult, Error, ProcessSwingParams>> = {}) => {
    const queryClient = useQueryClient();

    return useMutation(
        orpc.skills.processSwing.mutationOptions({
            onSuccess: () => {
                queryClient.invalidateQueries({ queryKey: APIROUTES.MINING.SESSION });
            },
            ...options,
        })
    );
};

export default useProcessSwing;
