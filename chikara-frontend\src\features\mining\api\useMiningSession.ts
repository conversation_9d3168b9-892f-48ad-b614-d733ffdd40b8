import { useQuery } from "@tanstack/react-query";
import { orpc, QueryOptions } from "@/lib/orpc";
import type { MiningSession } from "@/features/mining/types/mining";

const useMiningSession = (options: QueryOptions = {}) => {
    return useQuery(
        orpc.skills.getMiningSession.queryOptions({
            staleTime: 30000, // 30 seconds
            ...options,
        })
    );
};

export default useMiningSession;
